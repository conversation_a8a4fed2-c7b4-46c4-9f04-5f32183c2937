[package]
name = "nx"
version = "0.1.0"
edition = "2024"

[dependencies]
tokio = { version = "1", features = ["full"] }
reqwest = { version = "0.11", features = ["json", "stream", "gzip", "brotli", "deflate", "rustls-tls"] }
flate2 = "1"
tar = "0.4"
clap = { version = "4", features = ["derive"] }
indicatif = "0.17"
serde = { version = "1", features = ["derive"] }
serde_json = "1"
rayon = "1"
tokio-stream = "0.1"
futures-util = "0.3"

// Registry module for managing package registry operations

use std::collections::HashMap;
use std::fs;
use std::io::{self, BufReader, BufWriter};
use std::path::Path;
use serde::{Serialize, Deserialize};

#[derive(Serialize, Deserialize, Clone)]
pub struct PackageInfo {
    pub name: String,
    pub version: String,
    pub description: String,
    pub url: String,
}

#[derive(Default)]
pub struct Registry {
    packages: HashMap<String, PackageInfo>,
}

impl Registry {
    pub fn new() -> Self {
        Registry {
            packages: HashMap::new(),
        }
    }

    pub fn load_from_disk(&mut self, path: &Path) -> io::Result<()> {
        if path.exists() {
            let file = fs::File::open(path)?;
            let reader = BufReader::new(file);
            let loaded_packages: HashMap<String, PackageInfo> = serde_json::from_reader(reader)?;
            self.packages = loaded_packages;
        }
        Ok(())
    }

    pub fn save_to_disk(&self, path: &Path) -> io::Result<()> {
        let file = fs::File::create(path)?;
        let writer = BufWriter::new(file);
        serde_json::to_writer(writer, &self.packages)?;
        Ok(())
    }

    pub fn add_package(&mut self, package: PackageInfo) {
        self.packages.insert(package.name.clone(), package);
    }

    pub fn get_package(&self, name: &str) -> Option<&PackageInfo> {
        self.packages.get(name)
    }

    pub fn list_packages(&self) -> Vec<&PackageInfo> {
        self.packages.values().collect()
    }

    pub fn remove_package(&mut self, name: &str) -> Option<PackageInfo> {
        self.packages.remove(name)
    }

    pub fn is_installed(&self, name: &str) -> bool {
        self.packages.contains_key(name)
    }
}

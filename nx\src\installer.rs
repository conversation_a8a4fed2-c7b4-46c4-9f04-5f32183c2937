// Installer module for package management

use crate::registry::{Registry, PackageInfo};
use crate::progress::{ProgressBar, show_status, show_spinner};
use crate::utils::{ensure_directory_exists, get_config_directory, sanitize_package_name};
use reqwest::Client;
use std::fs;
use std::path::{Path, PathBuf};
use futures_util::StreamExt;

#[derive(Debug)]
pub enum InstallerError {
    NetworkError(reqwest::Error),
    IoError(std::io::Error),
    ParseError(String),
    PackageNotFound(String),
    AlreadyInstalled(String),
}

impl std::fmt::Display for InstallerError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            InstallerError::NetworkError(e) => write!(f, "Network error: {}", e),
            InstallerError::IoError(e) => write!(f, "IO error: {}", e),
            InstallerError::ParseError(e) => write!(f, "Parse error: {}", e),
            InstallerError::PackageNotFound(e) => write!(f, "Package not found: {}", e),
            InstallerError::AlreadyInstalled(e) => write!(f, "Package already installed: {}", e),
        }
    }
}

impl std::error::Error for InstallerError {}

impl From<reqwest::Error> for InstallerError {
    fn from(error: reqwest::Error) -> Self {
        InstallerError::NetworkError(error)
    }
}

impl From<std::io::Error> for InstallerError {
    fn from(error: std::io::Error) -> Self {
        InstallerError::IoError(error)
    }
}

pub struct PackageInstaller {
    registry: Registry,
    client: Client,
    install_dir: PathBuf,
}

impl PackageInstaller {
    pub fn new() -> Result<Self, InstallerError> {
        let config_dir = get_config_directory()
            .ok_or_else(|| InstallerError::IoError(std::io::Error::new(
                std::io::ErrorKind::NotFound,
                "Could not determine config directory"
            )))?;
        
        let install_dir = config_dir.join("packages");
        ensure_directory_exists(&install_dir)?;
        
        let mut registry = Registry::new();
        registry.load_from_disk(&config_dir.join("registry.json"))?;
        
        Ok(PackageInstaller {
            registry,
            client: Client::new(),
            install_dir,
        })
    }

    pub async fn install(&mut self, package_name: &str) -> Result<(), InstallerError> {
        let sanitized_name = sanitize_package_name(package_name);
        
        // Check if already installed
        if self.registry.is_installed(&sanitized_name) {
            return Err(InstallerError::AlreadyInstalled(sanitized_name));
        }

        show_spinner(&format!("Searching for package '{}'...", package_name));
        
        // Search for package in multiple registries
        let package_info = self.search_package(&sanitized_name).await?;
        
        println!("\nFound package: {} v{}", package_info.name, package_info.version);
        println!("Description: {}", package_info.description);
        
        // Download and install
        self.download_and_install(&package_info).await?;
        
        // Register as installed
        self.registry.add_package(package_info.clone());
        self.registry.save_to_disk(&self.get_registry_path())?;
        
        show_status(&format!("Successfully installed {}", package_name));
        Ok(())
    }

    pub fn list_installed(&self) -> Vec<&PackageInfo> {
        self.registry.list_packages()
    }

    pub async fn remove(&mut self, package_name: &str) -> Result<(), InstallerError> {
        let sanitized_name = sanitize_package_name(package_name);
        
        if !self.registry.is_installed(&sanitized_name) {
            return Err(InstallerError::PackageNotFound(sanitized_name));
        }

        let package_dir = self.install_dir.join(&sanitized_name);
        if package_dir.exists() {
            fs::remove_dir_all(&package_dir)?;
        }

        self.registry.remove_package(&sanitized_name);
        self.registry.save_to_disk(&self.get_registry_path())?;
        
        show_status(&format!("Successfully removed {}", package_name));
        Ok(())
    }

    async fn search_package(&self, name: &str) -> Result<PackageInfo, InstallerError> {
        // Search in npm registry as an example
        let url = format!("https://registry.npmjs.org/{}", name);
        
        let response = self.client.get(&url).send().await?;
        
        if !response.status().is_success() {
            return Err(InstallerError::PackageNotFound(name.to_string()));
        }

        let data: serde_json::Value = response.json().await?;
        
        let latest_version = data["dist-tags"]["latest"]
            .as_str()
            .ok_or_else(|| InstallerError::ParseError("Could not find latest version".to_string()))?;

        let version_data = &data["versions"][latest_version];
        
        let download_url = version_data["dist"]["tarball"]
            .as_str()
            .ok_or_else(|| InstallerError::ParseError("Could not find download URL".to_string()))?;

        Ok(PackageInfo {
            name: name.to_string(),
            version: latest_version.to_string(),
            description: version_data["description"]
                .as_str()
                .unwrap_or("No description available")
                .to_string(),
            url: download_url.to_string(),
        })
    }

    async fn download_and_install(&self, package: &PackageInfo) -> Result<(), InstallerError> {
        let package_dir = self.install_dir.join(&package.name);
        ensure_directory_exists(&package_dir)?;

        // Download package
        let response = self.client.get(&package.url).send().await?;
        let total_size = response.content_length().unwrap_or(0);
        
        let mut progress_bar = ProgressBar::new(total_size as usize);
        println!("Downloading {} v{}...", package.name, package.version);
        
        let mut downloaded = 0;
        let mut stream = response.bytes_stream();
        let mut file_data = Vec::new();
        
        while let Some(chunk) = futures_util::StreamExt::next(&mut stream).await {
            let chunk = chunk?;
            file_data.extend_from_slice(&chunk);
            downloaded += chunk.len();
            progress_bar.update(downloaded);
        }
        
        progress_bar.finish();
        
        // Extract tarball
        println!("Extracting package...");
        self.extract_tarball(&file_data, &package_dir)?;
        
        Ok(())
    }

    fn extract_tarball(&self, data: &[u8], target_dir: &Path) -> Result<(), InstallerError> {
        use flate2::read::GzDecoder;
        use tar::Archive;
        
        let decoder = GzDecoder::new(data);
        let mut archive = Archive::new(decoder);
        
        archive.unpack(target_dir)?;
        Ok(())
    }

    fn get_registry_path(&self) -> PathBuf {
        get_config_directory()
            .unwrap_or_else(|| PathBuf::from("./"))
            .join("registry.json")
    }
}

// Public API functions
pub async fn install(package: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut installer = PackageInstaller::new()?;
    installer.install(package).await?;
    Ok(())
}

pub async fn install_with_options(package: &str, force: bool) -> Result<(), Box<dyn std::error::Error>> {
    let mut installer = PackageInstaller::new()?;
    
    if force && installer.registry.is_installed(&crate::utils::sanitize_package_name(package)) {
        // Force reinstall: remove first, then install
        match installer.remove(package).await {
            Ok(_) => {},
            Err(InstallerError::PackageNotFound(_)) => {}, // Ignore if not found
            Err(e) => return Err(Box::new(e)),
        }
    }
    
    installer.install(package).await?;
    Ok(())
}

pub async fn list() -> Result<(), Box<dyn std::error::Error>> {
    let installer = PackageInstaller::new()?;
    let packages = installer.list_installed();
    
    if packages.is_empty() {
        println!("No packages installed.");
    } else {
        println!("Installed packages:");
        for package in packages {
            println!("  {} v{} - {}", package.name, package.version, package.description);
        }
    }
    Ok(())
}

pub async fn remove(package: &str) -> Result<(), Box<dyn std::error::Error>> {
    let mut installer = PackageInstaller::new()?;
    installer.remove(package).await?;
    Ok(())
}

pub async fn search(query: &str) -> Result<(), Box<dyn std::error::Error>> {
    let installer = PackageInstaller::new()?;
    show_spinner(&format!("Searching for packages matching '{}'...", query));
    
    // For demonstration, search npm registry
    let url = format!("https://registry.npmjs.org/-/search?text={}&size=10", query);
    let response = installer.client.get(&url).send().await?;
    
    if !response.status().is_success() {
        println!("\nFailed to search packages");
        return Ok(());
    }

    let data: serde_json::Value = response.json().await?;
let packages = data["objects"].as_array().cloned().unwrap_or_else(Vec::new);
    
    println!("\nSearch results for '{}':", query);
    if packages.is_empty() {
        println!("No packages found.");
    } else {
        for (i, pkg) in packages.iter().enumerate() {
            let name = pkg["package"]["name"].as_str().unwrap_or("Unknown");
            let version = pkg["package"]["version"].as_str().unwrap_or("Unknown");
            let description = pkg["package"]["description"].as_str().unwrap_or("No description");
            
            println!("  {}. {} v{}", i + 1, name, version);
            println!("     {}", description);
        }
    }
    
    Ok(())
}

pub async fn info(package: &str) -> Result<(), Box<dyn std::error::Error>> {
    let installer = PackageInstaller::new()?;
    
    // Check if installed locally first
    if let Some(local_pkg) = installer.registry.get_package(&crate::utils::sanitize_package_name(package)) {
        println!("Package: {} (installed)", local_pkg.name);
        println!("Version: {}", local_pkg.version);
        println!("Description: {}", local_pkg.description);
        println!("Download URL: {}", local_pkg.url);
        return Ok(());
    }
    
    // Fetch from registry
    show_spinner(&format!("Fetching information for '{}'...", package));
    
    match installer.search_package(package).await {
        Ok(package_info) => {
            println!("\nPackage: {} (available)", package_info.name);
            println!("Version: {}", package_info.version);
            println!("Description: {}", package_info.description);
            println!("Download URL: {}", package_info.url);
        }
        Err(InstallerError::PackageNotFound(_)) => {
            println!("\nPackage '{}' not found in registry.", package);
        }
        Err(e) => return Err(Box::new(e)),
    }
    
    Ok(())
}

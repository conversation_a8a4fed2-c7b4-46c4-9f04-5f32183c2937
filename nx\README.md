# NX Package Manager

A fast, reliable package manager written in Rust for installing and managing software packages.

## Features

- **Fast Downloads**: Efficient streaming downloads with progress bars
- **Multiple Registries**: Support for different package registries (currently npm as example)
- **Local Package Management**: Track installed packages with a local registry
- **Command Line Interface**: Intuitive CLI with comprehensive help system
- **Force Reinstall**: Option to force reinstall packages
- **Search Functionality**: Search for packages in the registry
- **Package Information**: Get detailed information about packages
- **Cross-Platform**: Works on Windows, macOS, and Linux

## Installation

### Build from Source

1. Make sure you have Rust installed (https://rustup.rs/)
2. Clone this repository:
   ```bash
   git clone <your-repo-url>
   cd nx
   ```
3. Build the release version:
   ```bash
   cargo build --release
   ```
4. The binary will be available at `target/release/nx` (or `nx.exe` on Windows)

### Add to PATH

For easier usage, add the binary to your system PATH.

## Usage

### Basic Commands

#### Install a Package
```bash
nx install <package-name>
```

#### Install with Force (Reinstall)
```bash
nx install <package-name> --force
# or
nx install <package-name> -f
```

#### List Installed Packages
```bash
nx list
```

#### Remove a Package
```bash
nx remove <package-name>
```

#### Search for Packages
```bash
nx search <search-term>
```

#### Get Package Information
```bash
nx info <package-name>
```

#### Help
```bash
nx --help
nx <command> --help
```

### Examples

```bash
# Install lodash
nx install lodash

# Search for react packages
nx search react

# Get information about a package
nx info express

# List all installed packages
nx list

# Remove a package
nx remove lodash

# Force reinstall a package
nx install lodash --force
```

## Configuration

NX stores its configuration and installed packages in:
- **Windows**: `%USERPROFILE%\.config\nx\`
- **macOS/Linux**: `~/.config/nx/`

The directory structure:
```
~/.config/nx/
├── packages/          # Installed packages
└── registry.json      # Local package registry
```

## Architecture

The NX package manager is built with a modular architecture:

- **CLI Module** (`src/cli.rs`): Command-line interface handling
- **Installer Module** (`src/installer.rs`): Core package installation logic
- **Registry Module** (`src/registry.rs`): Local package registry management
- **Progress Module** (`src/progress.rs`): Progress bars and status display
- **Utils Module** (`src/utils.rs`): Common utility functions

## Development

### Project Structure

```
src/
├── main.rs          # Application entry point
├── cli.rs           # Command-line interface
├── installer.rs     # Package installation logic
├── registry.rs      # Registry management
├── progress.rs      # Progress display utilities
└── utils.rs         # Common utilities
```

### Dependencies

- `tokio`: Async runtime
- `reqwest`: HTTP client for downloads
- `clap`: Command-line parsing
- `serde`: Serialization/deserialization
- `indicatif`: Progress bars
- `tar`: Archive extraction
- `flate2`: Gzip decompression

### Building

```bash
# Debug build
cargo build

# Release build
cargo build --release

# Run tests
cargo test

# Check code
cargo check
```

## Registry Support

Currently, NX uses the npm registry as an example implementation. You can extend it to support other registries by:

1. Implementing additional search methods in `PackageInstaller`
2. Modifying the `search_package` function to support multiple registry types
3. Adding registry configuration options

## Error Handling

NX provides comprehensive error handling for:
- Network errors (connection issues, timeouts)
- I/O errors (file system operations)
- Parse errors (malformed registry responses)
- Package not found errors
- Already installed package errors

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Roadmap

- [ ] Support for multiple package registries
- [ ] Package dependency resolution
- [ ] Package scripts execution
- [ ] Configuration file support
- [ ] Plugin system
- [ ] Package verification and checksums
- [ ] Rollback functionality
- [ ] Package update notifications

## Performance

NX is designed for performance:
- Streaming downloads minimize memory usage
- Concurrent operations where possible
- Efficient local registry storage
- Minimal startup time

## Security

- Downloads use HTTPS by default
- Package names are sanitized
- Safe file system operations
- No arbitrary code execution during installation

## Support

For issues, questions, or contributions, please use the GitHub issues page.

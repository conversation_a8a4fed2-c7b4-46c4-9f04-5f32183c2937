// Progress module for displaying installation and operation progress

use std::io::{self, Write};

pub struct ProgressBar {
    current: usize,
    total: usize,
    width: usize,
}

impl ProgressBar {
    pub fn new(total: usize) -> Self {
        ProgressBar {
            current: 0,
            total,
            width: 50,
        }
    }

    pub fn update(&mut self, current: usize) {
        self.current = current;
        self.display();
    }

    pub fn increment(&mut self) {
        if self.current < self.total {
            self.current += 1;
            self.display();
        }
    }

    pub fn finish(&mut self) {
        self.current = self.total;
        self.display();
        println!(); // New line after completion
    }

    fn display(&self) {
        let progress = if self.total == 0 {
            0.0
        } else {
            self.current as f64 / self.total as f64
        };
        
        let filled = (progress * self.width as f64) as usize;
        let empty = self.width - filled;

        print!("\r[{}{}] {}/{} ({:.1}%)",
            "=".repeat(filled),
            " ".repeat(empty),
            self.current,
            self.total,
            progress * 100.0
        );
        
        io::stdout().flush().unwrap();
    }
}

pub fn show_spinner(message: &str) {
    print!("{} ", message);
    io::stdout().flush().unwrap();
    // TODO: Implement actual spinner animation
}

pub fn show_status(message: &str) {
    println!("✓ {}", message);
}

// CLI module for handling command-line interface functionality

use clap::{Arg, Command};

pub fn build_cli() -> Command {
    Command::new("nx")
        .about("A Rust-based package installer and manager")
        .version(env!("CARGO_PKG_VERSION"))
        .author("NX Package Manager")
        .long_about("NX is a fast, reliable package manager for installing and managing software packages.\n\nExamples:\n  nx install lodash\n  nx list\n  nx remove lodash")
        .subcommand_required(true)
        .arg_required_else_help(true)
        .subcommand(
            Command::new("install")
                .about("Install a package")
                .long_about("Install a package from the registry. The package will be downloaded and extracted to the local packages directory.")
                .arg(
                    Arg::new("package")
                        .help("The package name to install")
                        .long_help("The name of the package to install from the registry")
                        .required(true)
                        .index(1),
                )
                .arg(
                    Arg::new("force")
                        .help("Force reinstall even if already installed")
                        .long("force")
                        .short('f')
                        .action(clap::ArgAction::SetTrue),
                ),
        )
        .subcommand(
            Command::new("list")
                .about("List installed packages")
                .long_about("Display all currently installed packages with their versions and descriptions."),
        )
        .subcommand(
            Command::new("remove")
                .about("Remove a package")
                .long_about("Remove an installed package and clean up its files.")
                .arg(
                    Arg::new("package")
                        .help("The package name to remove")
                        .long_help("The name of the package to remove from the system")
                        .required(true)
                        .index(1),
                ),
        )
        .subcommand(
            Command::new("search")
                .about("Search for packages")
                .long_about("Search for packages in the registry.")
                .arg(
                    Arg::new("query")
                        .help("Search query")
                        .long_help("The search term to look for in package names and descriptions")
                        .required(true)
                        .index(1),
                ),
        )
        .subcommand(
            Command::new("info")
                .about("Show package information")
                .long_about("Display detailed information about a package.")
                .arg(
                    Arg::new("package")
                        .help("The package name")
                        .long_help("The name of the package to get information about")
                        .required(true)
                        .index(1),
                ),
        )
}

pub async fn handle_matches(matches: &clap::ArgMatches) -> Result<(), Box<dyn std::error::Error>> {
    match matches.subcommand() {
        Some(("install", sub_matches)) => {
            let package = sub_matches
                .get_one::<String>("package")
                .expect("Package name is required");
            let force = sub_matches.get_flag("force");
            crate::installer::install_with_options(package, force).await?;
            Ok(())
        }
        Some(("list", _)) => {
            crate::installer::list().await?;
            Ok(())
        }
        Some(("remove", sub_matches)) => {
            let package = sub_matches
                .get_one::<String>("package")
                .expect("Package name is required");
            crate::installer::remove(package).await?;
            Ok(())
        }
        Some(("search", sub_matches)) => {
            let query = sub_matches
                .get_one::<String>("query")
                .expect("Search query is required");
            crate::installer::search(query).await?;
            Ok(())
        }
        Some(("info", sub_matches)) => {
            let package = sub_matches
                .get_one::<String>("package")
                .expect("Package name is required");
            crate::installer::info(package).await?;
            Ok(())
        }
        _ => unreachable!("Exhausted list of subcommands and subcommand_required prevents `None`"),
    }
}

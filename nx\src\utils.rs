// Utils module for common utility functions

use std::fs;
use std::path::Path;

pub fn ensure_directory_exists(path: &Path) -> Result<(), std::io::Error> {
    if !path.exists() {
        fs::create_dir_all(path)?;
    }
    Ok(())
}

pub fn file_exists(path: &Path) -> bool {
    path.exists() && path.is_file()
}

pub fn directory_exists(path: &Path) -> bool {
    path.exists() && path.is_dir()
}

pub fn get_home_directory() -> Option<std::path::PathBuf> {
    std::env::var("HOME")
        .or_else(|_| std::env::var("USERPROFILE"))
        .ok()
        .map(std::path::PathBuf::from)
}

pub fn get_config_directory() -> Option<std::path::PathBuf> {
    get_home_directory().map(|home| home.join(".config").join("nx"))
}

pub fn sanitize_package_name(name: &str) -> String {
    name.chars()
        .filter(|c| c.is_alphanumeric() || *c == '-' || *c == '_')
        .collect()
}

pub fn format_file_size(bytes: u64) -> String {
    const UNITS: &[&str] = &["B", "KB", "MB", "GB", "TB"];
    let mut size = bytes as f64;
    let mut unit_index = 0;

    while size >= 1024.0 && unit_index < UNITS.len() - 1 {
        size /= 1024.0;
        unit_index += 1;
    }

    if unit_index == 0 {
        format!("{} {}", bytes, UNITS[unit_index])
    } else {
        format!("{:.2} {}", size, UNITS[unit_index])
    }
}
